import axios from 'axios';

async function testItamarStats() {
  try {
    const itamarId = '67f2bd3317ec7db8bcd4f087';
    const apiUrl = 'https://proclubs-stats-server.duckdns.org';
    
    console.log('Testing deployed API...');
    console.log(`Calling: ${apiUrl}/player/${itamarId}`);
    
    const response = await axios.get(`${apiUrl}/player/${itamarId}`, {
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      })
    });
    
    console.log('\n=== API Response ===');
    console.log('Status:', response.status);
    console.log('Player current season stats:', {
      games: response.data.currentSeason.games,
      goals: response.data.currentSeason.goals,
      assists: response.data.currentSeason.assists,
      cleanSheets: response.data.currentSeason.cleanSheets,
      playerOfTheMatch: response.data.currentSeason.playerOfTheMatch,
      avgRating: response.data.currentSeason.avgRating
    });
    
    console.log('\n=== Full Response ===');
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error: any) {
    console.error('Error testing Itamar stats:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testItamarStats();
