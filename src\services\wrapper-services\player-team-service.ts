import { inject, injectable } from "tsyringe";
import logger from "../../config/logger";
import { BadRequestError } from "../../errors";
import { IPlayerRepository, IPlayerService } from "../../interfaces/player";
import { ITeamRepository } from "../../interfaces/team";
import { IPlayerTeamService } from "../../interfaces/wrapper-services/player-team-service.interface";
import { transactionService } from "../util-services/transaction-service";
import { INewsService } from "../../interfaces/news";

@injectable()
export class PlayerTeamService implements IPlayerTeamService {
  private playerRepository: IPlayerRepository;
  private teamRepository: ITeamRepository;
  private newsService: INewsService;
  private playerService: IPlayerService;

  constructor(@inject("ITeamRepository") teamService: ITeamRepository,
    @inject("IPlayerRepository") playerRepository: IPlayerRepository,
    @inject("INewsService") newsService: INewsService,
    @inject("IPlayerService") playerService: IPlayerService) {
    this.teamRepository = teamService;
    this.playerRepository = playerRepository;
    this.newsService = newsService;
    this.playerService = playerService;
  }

  async addPlayerToTeam(playerId: string, teamId: string): Promise<void> {
    logger.info(`PlayerTeamService: adding player ${playerId} to team ${teamId}`);
    const player = await this.playerRepository.getPlayerById(playerId);
    let oldTeam;

    if (player.team) {
      try {
        oldTeam = await this.teamRepository.getTeamById(player.team.toString());
        await this.removePlayerFromTeam(playerId, player.team.toString(), true); // true = isPartOfTransfer
      }

      catch (err) {
        throw err;
      }
    };
    const team = await this.teamRepository.getTeamById(teamId);

    if (team.players.includes(player._id as any)) {
      throw new BadRequestError(`Player ${player.id} is already in team ${teamId}`);
    }

    player.team = team._id as any;
    if (team.currentSeason) {
      player.currentSeason = {
        league: team.league!,
        seasonNumber: team.currentSeason.seasonNumber,
        team: team._id as any,
        stats: {
          assists: 0,
          goals: 0,
          avgRating: 0,
          cleanSheets: 0,
          playerOfTheMatch: 0,
          games: 0,
        },
      };
    }
    team.players.push(player._id as any);

    // Add transfer history entry
    const transferEntry = {
      fromTeam: oldTeam ? {
        id: oldTeam.id,
        name: oldTeam.name,
        imgUrl: oldTeam.imgUrl
      } : null,
      toTeam: {
        id: team.id,
        name: team.name,
        imgUrl: team.imgUrl
      },
      transferDate: new Date(),
      seasonNumber: team.currentSeason?.seasonNumber || 1,
      league: team.league!
    };

    if (!player.transferHistory) {
      player.transferHistory = [];
    }
    player.transferHistory.push(transferEntry);

    if (oldTeam)
      console.log('From team: ', oldTeam.id, oldTeam.name, oldTeam.imgUrl);

    
    console.log('To team: ', team.id, team.name, team.imgUrl);

    this.newsService.addNews({
      type: 'Transfer',
      date: new Date(),
      content: `HERE WE GO! ${player.name}: ${oldTeam ? oldTeam.name : 'Free Agent'} ➡ ${team.name} `,
      createdBy: 'Avi Vaknin',
      title: `A new signing for ${team.name}`,
      transferData: {
        fromTeam: {
          id: oldTeam ? oldTeam.id : '',
          name: oldTeam ? oldTeam.name : 'Free Agent',
          imgUrl: oldTeam ? oldTeam.imgUrl : ''
        },
        toTeam: {
          id: team.id,
          name: team.name,
          imgUrl: team.imgUrl
        },
        playerDetails: {
          id: player.id,
          name: player.name,
          imgUrl: player.imgUrl
        }
      }
    });

    await transactionService.withTransaction(async (session) => {
      await player.save({ session });
      await team.save({ session });
    });

    // Clear free agents cache since a player was added to a team
    await this.playerService.clearFreeAgentsCache();
  }

  async removePlayerFromTeam(playerId: string, teamId: string, isPartOfTransfer: boolean = false): Promise<void> {
    logger.info(`PlayerTeamService:  removing player ${playerId} from team ${teamId}`);

    const [player, team] = await Promise.all([this.playerRepository.getPlayerById(playerId), this.teamRepository.getTeamById(teamId)]);

    if (!player.team?.equals(team._id as any)) {
      throw new BadRequestError(`Player ${player.id} is not in team ${teamId}`);
    }

    player.team = null;
    if (player.currentSeason) {
      player.seasonsHistory.push(player.currentSeason);
      player.currentSeason = undefined;
    }

    // Add transfer history entry only if this is NOT part of a transfer to another team
    // (to avoid duplicate entries when player moves from team A to team B)
    if (!isPartOfTransfer) {
      const transferEntry = {
        fromTeam: {
          id: team.id,
          name: team.name,
          imgUrl: team.imgUrl
        },
        toTeam: null, // Player becomes free agent
        transferDate: new Date(),
        seasonNumber: team.currentSeason?.seasonNumber || 1,
        league: team.league!
      };

      if (!player.transferHistory) {
        player.transferHistory = [];
      }
      player.transferHistory.push(transferEntry);
    }

    team.players = team.players.filter((id) => !id.equals(player._id as any));

    await transactionService.withTransaction(async (session) => {
      await player.save({ session });
      await team.save({ session });
    });

    // Clear free agents cache since a player became a free agent
    await this.playerService.clearFreeAgentsCache();
  }

  async deletePlayer(_playerId: string) {
    // logger.info(`PlayerTeamService: deleting player ${_playerId}`);
    // const player = await this.playerRepository.getPlayerById(_playerId);
    // await transactionService.withTransaction(async (session) => {
    //   if (player.team) {
    //     await this.teamRepository.removePlayerFromTeam(player.team, player.id, session);
    //   }
    //   await this.playerRepository.deletePlayer(player.id, session);
    // });
  }

  async removeAllPlayersFromTeam(teamId: string): Promise<void> {
    logger.info(`PlayerTeamService: removing all players from team ${teamId}`);

    const team = await this.teamRepository.getTeamById(teamId);

    await transactionService.withTransaction(async (session) => {
      // Process each player sequentially
      for (const playerId of team.players) {
        const playerDetails = await this.playerRepository.getPlayerById(playerId);

        playerDetails.team = null;

        if (playerDetails.currentSeason) {
          playerDetails.seasonsHistory.push(playerDetails.currentSeason);
          playerDetails.currentSeason = undefined;
        }

        // Add transfer history entry for becoming free agent
        const transferEntry = {
          fromTeam: {
            id: team.id,
            name: team.name,
            imgUrl: team.imgUrl
          },
          toTeam: null, // Player becomes free agent
          transferDate: new Date(),
          seasonNumber: team.currentSeason?.seasonNumber || 1,
          league: team.league!
        };

        if (!playerDetails.transferHistory) {
          playerDetails.transferHistory = [];
        }
        playerDetails.transferHistory.push(transferEntry);

        await playerDetails.save({ session });
      }

      // Clear team's player list
      team.players = [];
      await team.save({ session });
    });

    // Clear free agents cache since multiple players became free agents
    await this.playerService.clearFreeAgentsCache();
  }
}