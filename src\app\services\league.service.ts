import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { ILeague } from '../shared/models/league-table.model';
import { LeagueTableRow } from '../shared/models/leagueTableTeam';
import { TopScorer } from '../shared/models/topscorer.model';
import { TopAssister } from '../shared/models/topassister.model';
import { AddFixtureDataRequest } from '../shared/models/addFixtureDataRequest';
import { FixtureDTO, PaginatedFixtureDTO } from '../shared/models/game.model';
import { LeagueDataStateService } from './state/league-data-state.service';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LeagueService {
  LEAGUE_CONTROLLER_URL = "league/";

  constructor(
    private apiService: ApiService,
    private leagueDataState: LeagueDataStateService
  ) { }

  async addLeague(name: string): Promise<ILeague> {
    const response = await this.apiService.post<ILeague>(`${this.LEAGUE_CONTROLLER_URL}`, name);

    return response.data;
  }

  async getLeagueById(id: string): Promise<ILeague> {
    const response = await this.apiService.get<ILeague>(`${this.LEAGUE_CONTROLLER_URL}/${id}`);

    return response.data;
  }

  async getLeagueTable(id: string, forceRefresh: boolean = false): Promise<LeagueTableRow[]> {
    console.log(`LeagueService: getLeagueTable called with id=${id}, forceRefresh=${forceRefresh}`);

    // Check if we have cached data and it's not stale
    if (!forceRefresh && !this.leagueDataState.isLeagueTableStale()) {
      const cachedData = this.leagueDataState.currentState.leagueTable;
      if (cachedData.length > 0) {
        console.log('LeagueService: Returning cached league table data', cachedData.length, 'teams');
        return cachedData;
      }
    }

    console.log('LeagueService: Fetching fresh league table data from server');
    // Set loading state
    this.leagueDataState.setLeagueTableLoading(true);

    try {
      const response = await this.apiService.get<LeagueTableRow[]>(`${this.LEAGUE_CONTROLLER_URL}${id}/table`);
      console.log('LeagueService: Received league table data from server', response.data.length, 'teams');

      // Update state with fresh data
      this.leagueDataState.updateLeagueTable(response.data);

      return response.data;
    } catch (error) {
      console.error('LeagueService: Error fetching league table:', error);
      this.leagueDataState.setLeagueTableLoading(false);
      throw error;
    }
  }

  async getAllLeagues(): Promise<ILeague[]> {
    const response = await this.apiService.get<ILeague[]>(`${this.LEAGUE_CONTROLLER_URL}`);

    return response.data;
  }

  async removeLeague(id: string): Promise<ILeague> {
    const response = await this.apiService.delete<ILeague>(`${this.LEAGUE_CONTROLLER_URL}/${id}`);

    return response.data;
  }

  async getTopScorers(leagueId: string, limit: number | null = null, forceRefresh: boolean = false): Promise<TopScorer[]> {
    // Check if we have cached data and it's not stale
    if (!forceRefresh && !this.leagueDataState.isTopScorersStale()) {
      const cachedData = this.leagueDataState.currentState.topScorers;
      if (cachedData.length > 0) {
        return limit ? cachedData.slice(0, limit) : cachedData;
      }
    }

    // Set loading state
    this.leagueDataState.setTopScorersLoading(true);

    try {
      const response = await this.apiService.get<TopScorer[]>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/topScorers?limit=${limit}`);

      // Update state with fresh data
      this.leagueDataState.updateTopScorers(response.data);

      return response.data;
    } catch (error) {
      this.leagueDataState.setTopScorersLoading(false);
      throw error;
    }
  }

  async getTopAssists(leagueId: string, limit: number | null = null, forceRefresh: boolean = false): Promise<TopAssister[]> {
    // Check if we have cached data and it's not stale
    if (!forceRefresh && !this.leagueDataState.isTopAssistersStale()) {
      const cachedData = this.leagueDataState.currentState.topAssisters;
      if (cachedData.length > 0) {
        return limit ? cachedData.slice(0, limit) : cachedData;
      }
    }

    // Set loading state
    this.leagueDataState.setTopAssistersLoading(true);

    try {
      const response = await this.apiService.get<TopAssister[]>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/topAssists?limit=${limit}`);

      // Update state with fresh data
      this.leagueDataState.updateTopAssisters(response.data);

      return response.data;
    } catch (error) {
      this.leagueDataState.setTopAssistersLoading(false);
      throw error;
    }
  }

  async getAllTimeTopScorers(leagueId: string, limit: number = 50): Promise<TopScorer[]> {
    const response = await this.apiService.get<TopScorer[]>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/allTimeTopScorers?limit=${limit}`);
    return response.data;
  }

  async getAllTimeTopAssisters(leagueId: string, limit: number = 50): Promise<TopAssister[]> {
    const response = await this.apiService.get<TopAssister[]>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/allTimeTopAssisters?limit=${limit}`);
    return response.data;
  }

  async getLeagueFixtures(leagueId: string,): Promise<FixtureDTO[]> {
    const response = await this.apiService.get<FixtureDTO[]>(`${this.LEAGUE_CONTROLLER_URL}league/round${leagueId}/fixtures`);

    return response.data;
  }

  async getPaginatedLeagueFixturesGames(leagueId: string, page: number, pageSize: number): Promise<PaginatedFixtureDTO> {
    const response = await this.apiService.get<PaginatedFixtureDTO>(`fixture/${this.LEAGUE_CONTROLLER_URL}${leagueId}/paginatedGames?page=${page}&pageSize=${pageSize}`);

    return response.data;
  }

  async createFixture(leagueId: string, createFixtureRequest: AddFixtureDataRequest): Promise<FixtureDTO> {
    const response = await this.apiService.post<FixtureDTO>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/createFixture`, createFixtureRequest);

    return response.data;
  }

  async getTOTW(leagueId: string, startDate: string, endDate: string): Promise<{honorableMentions: {}; teamOfTheWeek: {}}> {
    const response = await this.apiService.get<{honorableMentions: {}; teamOfTheWeek: {}}>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/teamOfTheWeek?startDate=${startDate}&endDate=${endDate}`);

    return response.data;
  }

  // Team of the Season methods
  async getTOTS(leagueId: string, seasonNumber: number, formation: string = '3-5-2'): Promise<any> {
    const response = await this.apiService.get<any>(`tots/league/${leagueId}/season/${seasonNumber}/${formation}`);
    return response.data;
  }

  async getAllTOTS(leagueId: string): Promise<any[]> {
    const response = await this.apiService.get<any[]>(`tots/league/${leagueId}`);
    return response.data;
  }

  async getAvailableTOTSSeasons(leagueId: string): Promise<number[]> {
    const response = await this.apiService.get<number[]>(`tots/league/${leagueId}/seasons`);
    return response.data;
  }

  async getAvailableTOTSFormations(leagueId: string, seasonNumber: number): Promise<string[]> {
    const response = await this.apiService.get<string[]>(`tots/league/${leagueId}/season/${seasonNumber}/formations`);
    return response.data;
  }

  async getSupportedTOTSFormations(): Promise<string[]> {
    const response = await this.apiService.get<string[]>(`tots/formations`);
    return response.data;
  }

  async generateTOTS(leagueId: string, seasonNumber: number, formation: string = '3-5-2'): Promise<any> {
    const response = await this.apiService.post<any>(`tots/league/${leagueId}/season/${seasonNumber}/generate`, { formation });
    return response.data;
  }

  async regenerateTOTS(leagueId: string, seasonNumber: number, formation: string = '3-5-2'): Promise<any> {
    const response = await this.apiService.put<any>(`tots/league/${leagueId}/season/${seasonNumber}/regenerate`, { formation });
    return response.data;
  }

  async clearServerCache(leagueId: string): Promise<void> {
    console.log('LeagueService: Clearing server cache for league', leagueId);
    try {
      const response = await this.apiService.post<any>(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/clear-cache`, {});
      console.log('LeagueService: Server cache cleared successfully', response.data);
    } catch (error) {
      console.error('LeagueService: Error clearing server cache:', error);
      throw error;
    }
  }

  // Team-League Management
  async addTeamToLeague(leagueId: string, teamId: string): Promise<void> {
    await this.apiService.put(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/addTeam`, { teamId });
  }

  async removeTeamFromLeague(leagueId: string, teamId: string): Promise<void> {
    await this.apiService.put(`${this.LEAGUE_CONTROLLER_URL}${leagueId}/removeTeam`, { teamId });
  }
}