<div class="signup-container">
    <div class="signup-card">
        <div class="signup-header">
            <h1 class="signup-title">Welcome to IPL Pro Clubs!</h1>
            <p class="signup-subtitle">Connect your player profile and join the ultimate football league experience</p>
            <div class="features-highlight">
                <div class="feature">
                    <i class="fas fa-trophy"></i>
                    <span>Track your stats</span>
                </div>
                <div class="feature">
                    <i class="fas fa-chart-line"></i>
                    <span>Compare performance</span>
                </div>
                <div class="feature">
                    <i class="fas fa-users"></i>
                    <span>Join your team</span>
                </div>
            </div>
        </div>

        <form [formGroup]="signUpFormGroup" (ngSubmit)="onSubmit()" class="signup-form">
            <div class="form-group" *ngFor="let control of formControls">
                <label class="form-label">
                    {{ control.displayText }}
                    <span class="required-indicator" *ngIf="isRequiredForm(control.control)">*</span>
                </label>

                <input
                    *ngIf="control.type === 'text-input'"
                    [formControlName]="control.field"
                    [maxlength]="control.maxLength!"
                    [placeholder]="'Enter your ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <input
                    *ngIf="control.type === 'email'"
                    [formControlName]="control.field"
                    type="email"
                    [maxlength]="control.maxLength!"
                    [placeholder]="'Enter your ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <input
                    *ngIf="control.type === 'password'"
                    [formControlName]="control.field"
                    type="password"
                    [placeholder]="'Enter your ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <!-- Error messages -->
                <div *ngIf="control.control.invalid && control.control.touched" class="error-message">
                    <span *ngIf="isRequiredForm(control.control)">{{ control.displayText }} is required</span>
                    <span *ngIf="isEmailInvalid(control.control)">Please enter a valid email address</span>
                    <span *ngIf="isPasswordTooShort(control.control)">Password must be at least 8 characters long</span>
                </div>
            </div>

            <!-- Player Association Section -->
            <div class="player-association-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-user-plus"></i>
                        Choose Your Player (Optional)
                    </h3>
                    <p class="section-subtitle">Connect with your player profile to track stats and manage your team</p>
                </div>

                <div class="player-toggle">
                    <button
                        type="button"
                        class="toggle-button"
                        (click)="togglePlayerSearch()"
                        [class.active]="showPlayerSearch">
                        <i class="fas fa-search" *ngIf="!showPlayerSearch"></i>
                        <i class="fas fa-times" *ngIf="showPlayerSearch"></i>
                        <span *ngIf="!showPlayerSearch">Find My Player</span>
                        <span *ngIf="showPlayerSearch">Skip This Step</span>
                    </button>
                </div>

                <!-- Player Search -->
                <div *ngIf="showPlayerSearch" class="player-search-container">
                    <form [formGroup]="playerSearchForm" class="player-search-form">
                        <div class="search-input-group">
                            <div class="input-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <input
                                type="text"
                                formControlName="searchTerm"
                                placeholder="Enter player name..."
                                class="search-input">
                            <button
                                type="button"
                                class="search-button"
                                (click)="searchPlayers()"
                                [disabled]="isSearching || !playerSearchForm.get('searchTerm')?.value">
                                <i class="fas fa-search" *ngIf="!isSearching"></i>
                                <i class="fas fa-spinner fa-spin" *ngIf="isSearching"></i>
                                <span *ngIf="!isSearching">Search</span>
                                <span *ngIf="isSearching">Searching...</span>
                            </button>
                        </div>
                    </form>

                    <!-- Selected Player Display -->
                    <div *ngIf="selectedPlayer" class="selected-player">
                        <div class="selected-player-card">
                            <img [src]="selectedPlayer.imgUrl || 'assets/Icons/User.jpg'"
                                 [alt]="selectedPlayer.name"
                                 class="selected-player-avatar">
                            <div class="selected-player-info">
                                <h4 class="selected-player-name">{{selectedPlayer.name}}</h4>
                                <div class="selected-player-details">
                                    <span class="position-badge" [ngClass]="'position-' + selectedPlayer.position.toLowerCase()">
                                        {{selectedPlayer.position}}
                                    </span>
                                    <span class="age-info">Age: {{selectedPlayer.age}}</span>
                                </div>
                            </div>
                            <button type="button" class="clear-selection" (click)="clearSelectedPlayer()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="association-notice">
                            <i class="fas fa-info-circle"></i>
                            <span>Association request will be sent after registration for admin approval</span>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div *ngIf="availablePlayers.length > 0 && !selectedPlayer" class="search-results">
                        <h4 class="results-title">Available Players ({{availablePlayers.length}})</h4>
                        <div class="players-grid">
                            <div *ngFor="let player of availablePlayers" class="player-result-card" (click)="selectPlayer(player)">
                                <img [src]="player.imgUrl || 'assets/Icons/User.jpg'"
                                     [alt]="player.name"
                                     class="player-result-avatar">
                                <div class="player-result-info">
                                    <h5 class="player-result-name">{{player.name}}</h5>
                                    <div class="player-result-details">
                                        <span class="position-badge" [ngClass]="'position-' + player.position.toLowerCase()">
                                            {{player.position}}
                                        </span>
                                        <span class="age-info">Age: {{player.age}}</span>
                                    </div>
                                </div>
                                <div class="select-indicator">
                                    <i class="fas fa-plus"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button
                type="submit"
                class="submit-button"
                [disabled]="signUpFormGroup.invalid || isLoading">
                <span *ngIf="!isLoading">Create Account</span>
                <span *ngIf="isLoading">Creating Account...</span>
            </button>
        </form>

        <div class="divider">or</div>

        <button
            type="button"
            class="google-signin-button"
            (click)="signUpWithGoogle()"
            [disabled]="isLoading">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-icon">
            <span>Sign up with Google</span>
        </button>

        <div class="login-link">
            <p>Already have an account?
                <a (click)="navigateToLogin()" class="link">Login here</a>
            </p>
        </div>

        <div class="browse-link">
            <p>Want to explore first?
                <a routerLink="/dashboard" class="link">Browse without signing up</a>
            </p>
        </div>
    </div>
</div>