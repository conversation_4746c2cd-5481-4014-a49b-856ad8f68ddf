/* === MODERN SIGN UP DESIGN === */

.signup-container {
    min-height: 100vh;
    background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--surface-primary) 50%,
        var(--bg-primary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 5px;
    font-family: var(--font-sans);
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg) 5px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md) 5px;
    }
}

.signup-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: var(--spacing-2xl);
    width: 100%;
    max-width: 600px;
    position: relative;
    backdrop-filter: blur(20px);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
        max-width: 500px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
        max-width: 100%;
    }
}

.signup-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);

    .signup-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .signup-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0 0 var(--spacing-lg) 0;
        font-weight: var(--font-weight-medium);
    }

    .features-highlight {
        display: flex;
        justify-content: center;
        gap: var(--spacing-xl);
        margin-top: var(--spacing-lg);

        @media (max-width: 768px) {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .feature {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);

            i {
                color: var(--primary);
                font-size: var(--text-base);
            }

            @media (max-width: 768px) {
                justify-content: center;
            }
        }
    }
}

.signup-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .form-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .required-indicator {
            color: var(--error);
            font-size: var(--text-xs);
        }
    }

    .form-input {
        background: var(--surface-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-primary);
        transition: all 0.3s ease;
        font-family: var(--font-sans);

        &::placeholder {
            color: var(--text-tertiary);
        }

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
            background: var(--surface-primary);
        }

        &:hover:not(:focus) {
            border-color: var(--border-secondary);
        }

        &.error {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(var(--error-rgb), 0.1);
        }
    }
}

.file-upload-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .file-upload-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
    }

    .file-upload-button {
        background: var(--surface-secondary);
        border: 2px dashed var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: var(--text-secondary);

        &:hover {
            border-color: var(--primary);
            background: var(--surface-tertiary);
            color: var(--text-primary);
        }

        i {
            font-size: var(--text-xl);
            margin-bottom: var(--spacing-sm);
            display: block;
        }
    }

    .file-input {
        display: none;
    }
}

.submit-button {
    background: linear-gradient(135deg, var(--primary), var(--accent-primary));
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-inverse);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: var(--spacing-md);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

        &::before {
            left: 100%;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
        opacity: 0.6;
    }
}

.google-signin-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        border-color: var(--border-secondary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    .google-icon {
        width: 20px;
        height: 20px;
    }
}

.login-link {
    text-align: center;
    margin-top: var(--spacing-xl);

    p {
        color: var(--text-secondary);
        font-size: var(--text-sm);
        margin: 0;
    }

    .link {
        color: var(--primary);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }
    }
}

.browse-link {
    text-align: center;
    margin-top: var(--spacing-md);

    p {
        color: var(--text-tertiary);
        font-size: var(--text-sm);
        margin: 0;
    }

    .link {
        color: var(--accent-primary);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
            color: var(--accent-hover);
            text-decoration: underline;
        }
    }
}

.divider {
    display: flex;
    align-items: center;
    margin: var(--spacing-xl) 0;
    color: var(--text-tertiary);
    font-size: var(--text-sm);

    &::before,
    &::after {
        content: '';
        flex: 1;
        height: 1px;
        background: var(--border-primary);
    }

    &::before {
        margin-right: var(--spacing-md);
    }

    &::after {
        margin-left: var(--spacing-md);
    }
}

/* === PLAYER ASSOCIATION SECTION === */
.player-association-section {
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-xl);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-lg);

        .section-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);

            i {
                color: var(--primary);
            }
        }

        .section-subtitle {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            margin: 0;
        }
    }

    .player-toggle {
        display: flex;
        justify-content: center;
        margin-bottom: var(--spacing-lg);

        .toggle-button {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--surface-primary);
            border: 2px solid var(--border-primary);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: var(--primary);
                background: var(--surface-hover);
            }

            &.active {
                background: var(--primary);
                border-color: var(--primary);
                color: white;
            }
        }
    }

    .player-search-container {
        animation: slideDown 0.3s ease-out;
    }

    .player-search-form {
        margin-bottom: var(--spacing-lg);

        .search-input-group {
            display: flex;
            align-items: center;
            background: var(--surface-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: border-color 0.3s ease;

            &:focus-within {
                border-color: var(--primary);
            }

            .input-icon {
                padding: var(--spacing-md);
                color: var(--text-secondary);
            }

            .search-input {
                flex: 1;
                padding: var(--spacing-md) 0;
                border: none;
                background: transparent;
                color: var(--text-primary);
                font-size: var(--text-base);

                &::placeholder {
                    color: var(--text-tertiary);
                }

                &:focus {
                    outline: none;
                }
            }

            .search-button {
                padding: var(--spacing-md) var(--spacing-lg);
                background: var(--primary);
                border: none;
                color: white;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.3s ease;
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);

                &:hover:not(:disabled) {
                    background: var(--primary-600);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            }
        }
    }

    .selected-player {
        margin-bottom: var(--spacing-lg);

        .selected-player-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
            background: var(--surface-primary);
            border: 2px solid var(--success-400);
            border-radius: var(--radius-lg);
            position: relative;

            .selected-player-avatar {
                width: 60px;
                height: 60px;
                border-radius: var(--radius-full);
                object-fit: cover;
                border: 2px solid var(--success-400);
            }

            .selected-player-info {
                flex: 1;

                .selected-player-name {
                    font-size: var(--text-lg);
                    font-weight: 600;
                    color: var(--text-primary);
                    margin: 0 0 var(--spacing-xs) 0;
                }

                .selected-player-details {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-md);

                    .position-badge {
                        padding: var(--spacing-xs) var(--spacing-sm);
                        border-radius: var(--radius-md);
                        font-size: var(--text-xs);
                        font-weight: 600;
                        text-transform: uppercase;
                    }

                    .age-info {
                        font-size: var(--text-sm);
                        color: var(--text-secondary);
                    }
                }
            }

            .clear-selection {
                position: absolute;
                top: var(--spacing-sm);
                right: var(--spacing-sm);
                width: 24px;
                height: 24px;
                border-radius: var(--radius-full);
                background: var(--error-500);
                border: none;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--text-xs);
                transition: background 0.3s ease;

                &:hover {
                    background: var(--error-600);
                }
            }
        }

        .association-notice {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: var(--info-100);
            border: 1px solid var(--info-300);
            border-radius: var(--radius-md);
            margin-top: var(--spacing-md);
            font-size: var(--text-sm);
            color: var(--info-700);

            i {
                color: var(--info-500);
            }
        }
    }

    .search-results {
        .results-title {
            font-size: var(--text-base);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-md) 0;
        }

        .players-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--spacing-md);

            .player-result-card {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
                padding: var(--spacing-md);
                background: var(--surface-primary);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-lg);
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;

                &:hover {
                    border-color: var(--primary);
                    transform: translateY(-2px);
                    box-shadow: var(--shadow-md);
                }

                .player-result-avatar {
                    width: 50px;
                    height: 50px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 2px solid var(--border-primary);
                }

                .player-result-info {
                    flex: 1;

                    .player-result-name {
                        font-size: var(--text-base);
                        font-weight: 600;
                        color: var(--text-primary);
                        margin: 0 0 var(--spacing-xs) 0;
                    }

                    .player-result-details {
                        display: flex;
                        align-items: center;
                        gap: var(--spacing-sm);

                        .position-badge {
                            padding: var(--spacing-xs) var(--spacing-sm);
                            border-radius: var(--radius-md);
                            font-size: var(--text-xs);
                            font-weight: 600;
                            text-transform: uppercase;
                        }

                        .age-info {
                            font-size: var(--text-sm);
                            color: var(--text-secondary);
                        }
                    }
                }

                .select-indicator {
                    width: 32px;
                    height: 32px;
                    border-radius: var(--radius-full);
                    background: var(--primary);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--text-sm);
                    transition: all 0.3s ease;
                }

                &:hover .select-indicator {
                    background: var(--primary-600);
                    transform: scale(1.1);
                }
            }
        }
    }
}

/* Position badge colors */
.position-gk { background: var(--warning-100); color: var(--warning-700); }
.position-def { background: var(--info-100); color: var(--info-700); }
.position-mid { background: var(--success-100); color: var(--success-700); }
.position-att { background: var(--error-100); color: var(--error-700); }

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}