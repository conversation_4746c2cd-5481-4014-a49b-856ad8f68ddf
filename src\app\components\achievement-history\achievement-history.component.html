<div class="achievement-history-container">
  <div class="header">
    <h3>
      <i class="fas fa-trophy"></i>
      Achievement History
    </h3>
    <div class="achievement-stats" *ngIf="hasAchievements()">
      <span class="stat-item">
        <i class="fas fa-award"></i>
        {{ getAchievementCount() }} Total
      </span>
      <span class="stat-item" *ngIf="getChampionshipCount() > 0">
        <i class="fas fa-trophy"></i>
        {{ getChampionshipCount() }} Championships
      </span>
      <span class="stat-item" *ngIf="entityType === 'player' && getTopScorerCount() > 0">
        <i class="fas fa-futbol"></i>
        {{ getTopScorerCount() }} Top Scorer
      </span>
      <span class="stat-item" *ngIf="entityType === 'player' && getTopAssistCount() > 0">
        <i class="fas fa-hands-helping"></i>
        {{ getTopAssistCount() }} Top Assists
      </span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-section">
    <div class="spinner"></div>
    <p>Loading achievement history...</p>
  </div>

  <!-- No Achievements State -->
  <div *ngIf="!isLoading && !hasAchievements()" class="no-achievements">
    <i class="fas fa-medal"></i>
    <h4>No Achievements Yet</h4>
    <p>{{ entityName }} hasn't earned any achievements yet. Keep playing to make history!</p>
  </div>

  <!-- Medal Display -->
  <div *ngIf="!isLoading && hasAchievements()" class="medals-container">
    <!-- Personal Achievements Section (for players) -->
    <div *ngIf="entityType === 'player' && getPersonalAchievements().length > 0" class="achievement-category">
      <div class="category-header">
        <h4>
          <i class="fas fa-user"></i>
          Personal Achievements
        </h4>
      </div>
      <div class="medals-grid">
        <div
          class="medal-card"
          *ngFor="let achievement of getPersonalAchievements()"
          [class.gold-medal]="isMedalType(achievement, 'gold')"
          [class.silver-medal]="isMedalType(achievement, 'silver')"
          [class.bronze-medal]="isMedalType(achievement, 'bronze')"
          [class.special-medal]="isMedalType(achievement, 'special')">

          <div class="medal-container">
            <div class="medal-circle" [style.background]="getMedalGradient(achievement)">
              <i [class]="getMedalIcon(achievement)"></i>
            </div>
            <div class="medal-ribbon"></div>
          </div>

          <div class="medal-info">
            <div class="medal-title">{{ getAchievementTitle(achievement) }}</div>
            <div class="medal-season">Season {{ achievement.seasonNumber }} ({{ achievement.year }})</div>
            <div class="medal-league">{{ achievement.leagueName }}</div>
            <div class="medal-stats" *ngIf="getAchievementStats(achievement)">
              {{ getAchievementStats(achievement) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Achievements Section -->
    <div class="achievement-category">
      <div class="category-header">
        <h4>
          <i class="fas fa-users"></i>
          {{ entityType === 'player' ? 'Team Achievements' : 'Team Achievements' }}
        </h4>
      </div>
      <div class="medals-grid">
        <div
          class="medal-card"
          *ngFor="let achievement of getTeamAchievements()"
          [class.gold-medal]="isMedalType(achievement, 'gold')"
          [class.silver-medal]="isMedalType(achievement, 'silver')"
          [class.bronze-medal]="isMedalType(achievement, 'bronze')"
          [class.special-medal]="isMedalType(achievement, 'special')">

          <div class="medal-container">
            <div class="medal-circle" [style.background]="getMedalGradient(achievement)">
              <i [class]="getMedalIcon(achievement)"></i>
            </div>
            <div class="medal-ribbon"></div>
          </div>

          <div class="medal-info">
            <div class="medal-title">{{ getAchievementTitle(achievement) }}</div>
            <div class="medal-season">Season {{ achievement.seasonNumber }} ({{ achievement.year }})</div>
            <div class="medal-league">{{ achievement.leagueName }}</div>
            <div class="medal-stats" *ngIf="getAchievementStats(achievement)">
              {{ getAchievementStats(achievement) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Achievement Summary -->
  <div *ngIf="!isLoading && hasAchievements()" class="achievement-summary">
    <h4>Achievement Summary</h4>
    <div class="summary-grid">
      <div class="summary-item" *ngFor="let type of getUniqueAchievementTypes()">
        <div class="summary-icon" [style.background-color]="getAchievementColor(type)">
          <i [class]="getAchievementIcon(type)"></i>
        </div>
        <div class="summary-content">
          <div class="summary-title">{{ type }}</div>
          <div class="summary-count">{{ getAchievementsByType(type).length }}x</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Most Recent Achievement Highlight -->
  <div *ngIf="!isLoading && hasAchievements() && getMostRecentAchievement()" class="recent-achievement">
    <h4>Most Recent Achievement</h4>
    <div class="recent-card" [style.border-color]="getMostRecentAchievement()!.color">
      <div class="recent-icon" [style.background-color]="getMostRecentAchievement()!.color">
        <i [class]="getMostRecentAchievement()!.icon"></i>
      </div>
      <div class="recent-content">
        <div class="recent-title">
          {{ getAchievementTitle(getMostRecentAchievement()!) }}
        </div>
        <div class="recent-subtitle">
          {{ getAchievementSubtitle(getMostRecentAchievement()!) }}
        </div>
        <div class="recent-description" *ngIf="getAchievementStats(getMostRecentAchievement()!)">
          {{ getAchievementStats(getMostRecentAchievement()!) }}
        </div>
      </div>
    </div>
  </div>
</div>
