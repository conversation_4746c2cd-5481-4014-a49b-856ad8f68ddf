import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PlayerService } from '../../services/player.service';
import { NotificationService } from '../../services/notification.service';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';
import { TeamService } from '../../services/team.service';
import { ConfigurationService } from '../../services/configuration.service';
import { DEFENSIVE_POSITIONS } from '../../shared/models/player.model';
import { ListOption } from '../../shared/models/list-option.model';
import { PLAYABLE_POSITIONS_OPTIONS } from '../top-scorers/top-scorers.definitions';
import { PermissionsService } from '../../services/permissions.service';
import { PlayerProfileCardComponent } from './player-profile-card/player-profile-card.component';
import { AuthService } from '../../services/auth.service';
import { PromotionalBannerComponent } from '../promotional-banner/promotional-banner.component';

@Component({
  selector: 'app-player-details',
  templateUrl: './player-details.component.html',
  styleUrls: ['./player-details.component.scss']
})
export class PlayerDetailsComponent {
  playerID: string = '';
  chosenPlayer: PlayerDTO | null = null;
  editPlayerMode: boolean = false;
  editedPlayerName: string | null = null;
  editedPlayerAge: number | null = null;
  editedPlayerPosition: string | null = null;
  editedPlayablePositions: string[] = [];
  editPlayerPhotoModel: FormData | null = null;
  positionOptions: ListOption[] = PLAYABLE_POSITIONS_OPTIONS;
  public isDefender: boolean = false;
  canEditPlayer: boolean = false;
  canRemoveFromTeam: boolean = false;
  isAuthenticated: boolean = false;
  isPlayerAssociated: boolean = false;
  promotionalMessage = PromotionalBannerComponent.MESSAGES.PLAYER_DETAILS;

  @ViewChild(PlayerProfileCardComponent) profileCard!: PlayerProfileCardComponent;

  constructor(private route: ActivatedRoute, private router: Router, private configurationService: ConfigurationService,
    private teamService: TeamService, private playerService: PlayerService, private notificationService: NotificationService,
    private permissionsService: PermissionsService, private authService: AuthService) { }

  ngOnInit() {
    // Subscribe to authentication state
    this.authService.isAuthenticated$.subscribe(
      isAuth => this.isAuthenticated = isAuth
    );

    // Subscribe to route parameter changes
    this.route.params.subscribe(async (params) => {
      if (params['id']) {
        this.playerID = params['id'];
        await this.loadPlayerData();
      }
    });

    // Initial load if no route params
    if (!this.playerID) {
      this.loadPlayerData();
    }
  }

  private async loadPlayerData(): Promise<void> {
    // Try to get player ID from different route parameter formats
    this.playerID = this.route.snapshot.paramMap.get('id') ||
                   this.route.snapshot.params['id'] ||
                   this.playerID;

    if (!this.playerID) {
      console.error('No player ID found in route parameters');
      return;
    }

    console.log('Loading player data for ID:', this.playerID);

    try {
      this.chosenPlayer = await this.playerService.getPlayerById(this.playerID);
      this.isDefender = DEFENSIVE_POSITIONS.includes(this.chosenPlayer.position);
    } catch (error) {
      console.error('Error loading player data:', error);
      return;
    }

    // Check if user can edit this player
    this.canEditPlayer = await this.permissionsService.canEditPlayer(this.playerID);

    // Check if user can remove this player from team (admin or team captain only)
    this.permissionsService.canRemovePlayerFromTeam(this.playerID).subscribe(
      canRemove => this.canRemoveFromTeam = canRemove
    );

    // Check if this player is associated with any user
    await this.checkPlayerAssociation();
  }

  private async checkPlayerAssociation(): Promise<void> {
    try {
      // Check if any user has this player associated
      const response = await this.authService.checkPlayerAssociation(this.playerID);
      this.isPlayerAssociated = response.isAssociated;
    } catch (error) {
      // If the API call fails, assume not associated and log the error
      console.warn('Failed to check player association status:', error);
      this.isPlayerAssociated = false;
    }
  }

  async setPlayerImage(file: File) {
    this.editPlayerPhotoModel = new FormData();
    this.editPlayerPhotoModel.append('file', file);

  }

  async onFileSelected($event: any) {
    if (!$event.target.files)
      return;

    const file: File = $event.target.files[0];

    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.notificationService.error('Please select a valid image file.');
      if (this.profileCard) {
        this.profileCard.onUploadError();
      }
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      this.notificationService.error('Image file size must be less than 10MB.');
      if (this.profileCard) {
        this.profileCard.onUploadError();
      }
      return;
    }

    try {
      this.setPlayerImage(file);
      await this.playerService.setPlayerImage(this.editPlayerPhotoModel!, this.playerID);

      // Trigger success feedback in profile card
      if (this.profileCard) {
        this.profileCard.onUploadSuccess();
      }

      this.notificationService.success('Profile picture updated successfully!');
    } catch (error) {
      console.error('Error uploading image:', error);

      // Trigger error feedback in profile card
      if (this.profileCard) {
        this.profileCard.onUploadError();
      }

      this.notificationService.error('Failed to upload profile picture. Please try again.');
    }
  }

  onEditClick() {
    this.editedPlayerName = this.chosenPlayer!.name;
    this.editedPlayerAge = this.chosenPlayer!.age;
    this.editedPlayerPosition = this.chosenPlayer!.position;
    this.editedPlayablePositions = [...(this.chosenPlayer?.playablePositions || [])];
    this.editPlayerMode = true;
  }

  onCancelEditClick() {
    this.editPlayerMode = false;
    this.editedPlayerName = null;
    this.editedPlayerAge = null;
    this.editedPlayerPosition = null;
    this.editedPlayablePositions = [];
    this.editPlayerPhotoModel = null;
  }

  async removePlayerFromTeam() {
    if (!this.chosenPlayer?.team) {
      this.notificationService.error('Player is not currently in a team');
      return;
    }

    const confirmRemove = confirm(`Are you sure you want to remove ${this.chosenPlayer.name} from ${this.chosenPlayer.team.name}?`);
    if (!confirmRemove) {
      return;
    }

    try {
      await this.teamService.removePlayerFromTeam(this.chosenPlayer.team.id, this.chosenPlayer.id);
      this.notificationService.success(`${this.chosenPlayer.name} removed from team successfully`);
      this.onArrowBackClick();
    } catch (error: any) {
      console.error('Error removing player from team:', error);
      this.notificationService.error(error.message || 'Failed to remove player from team');
    }
  }

  onArrowBackClick(): void {
    history.back();
  }

  async onSaveClick() {
    if (this.editPlayerPhotoModel) {
      await this.playerService.setPlayerImage(this.editPlayerPhotoModel!, this.playerID);
      this.notificationService.success(`${this.chosenPlayer!.name}'s image changed successfuly`);
      this.editPlayerPhotoModel = null;
    }

    if (this.chosenPlayer!.name !== this.editedPlayerName) {
      await this.playerService.renamePlayer(this.playerID, this.editedPlayerName!);
      this.notificationService.success(`Player renamed to ${this.editedPlayerName} successfuly`);
      this.editedPlayerName = null;
    };

    // Update age if changed
    if (this.chosenPlayer!.age !== this.editedPlayerAge) {
      await this.playerService.editPlayerAge(this.playerID, this.editedPlayerAge!);
      this.notificationService.success(`Player's age changed to: ${this.editedPlayerAge}`);
      this.editedPlayerAge = null;
    }

    if (this.chosenPlayer!.position !== this.editedPlayerPosition) {
      await this.playerService.editPlayerPosition(this.playerID, this.editedPlayerPosition!);
      this.notificationService.success(`Player's position changed to: ${this.editedPlayerPosition}`);
      this.editedPlayerPosition = null;
    }

    // Update playable positions if changed
    const currentPlayablePositions = this.chosenPlayer!.playablePositions || [];
    const hasPlayablePositionsChanged =
      this.editedPlayablePositions.length !== currentPlayablePositions.length ||
      this.editedPlayablePositions.some(pos => !currentPlayablePositions.includes(pos));

    if (hasPlayablePositionsChanged) {
      await this.playerService.editPlayerPlayablePositions(this.playerID, this.editedPlayablePositions);
      this.notificationService.success(`Player's playable positions updated`);
      this.editedPlayablePositions = [];
    }

    this.editPlayerMode = false;

    this.loadPlayerData();
  };

  isAdmin() {
    return this.authService.isAdmin();
  };

  onPositionChange($chosenOption: ListOption) {
    if (!$chosenOption) return;

    this.editedPlayerPosition = $chosenOption.value;
  }

  onNameChange(newName: string) {
    this.editedPlayerName = newName;
  }

  onAgeChange(newAge: number) {
    this.editedPlayerAge = newAge;
  }

  onPlayablePositionsChange(selectedPositions: ListOption[]) {
    this.editedPlayablePositions = selectedPositions.map(pos => pos.value);
  }

  public getAVGRatingColor(): string {
    const avgRating = this.chosenPlayer!.stats.avgRating;

    if (avgRating >= 7 && avgRating < 8.5) {
      return 'text-yellow';
    }
    else if (avgRating >= 8.5) {
      return 'text-green';
    }
    else {
      return 'text-red';
    }
  }

  navigateToTeamDetails(): void {
    if (this.chosenPlayer?.team?.id) {
      this.router.navigate(['/team-details', { id: this.chosenPlayer.team.id }]);
    }
  }

  onComparePlayer(): void {
    this.router.navigate(['/player-comparison', this.playerID]);
  }

  onViewSeasonHistory(): void {
    this.router.navigate(['/player-season-history', this.playerID]);
  }
}