import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { PlayerDTO, PlayerLastGamesForm } from '@pro-clubs-manager/shared-dtos';
import { IPlayerStats } from '../shared/models/player.model';
import { ITransferHistoryEntry } from '../shared/models/transfer-history.model';
import { PlayerComparisonData } from '../shared/models/player-comparison.model';
import { PlayerSeasonHistoryData } from '../shared/models/player-season-history.model';
import { PlayerDataStateService } from './state/player-data-state.service';

@Injectable({
  providedIn: 'root'
})
export class PlayerService {
  PLAYERS_CONTROLLER_URL = "player";

  constructor(
    private apiService: ApiService,
    private playerDataState: PlayerDataStateService
  ) { }

  async getAllPlayers(): Promise<PlayerDTO[]> {
    const response = await this.apiService.get<PlayerDTO[]>(`${this.PLAYERS_CONTROLLER_URL}/`);

    return response.data;
  }

  async getPlayerById(id: string, forceRefresh: boolean = false): Promise<PlayerDTO> {
    // Check if we have cached data and it's not stale
    if (!forceRefresh && !this.playerDataState.isPlayerStale(id)) {
      const cachedPlayer = this.playerDataState.getPlayer(id);
      if (cachedPlayer) {
        return cachedPlayer;
      }
    }

    // Set loading state
    this.playerDataState.setPlayerLoading(id, true);

    try {
      const response = await this.apiService.get<PlayerDTO>(`${this.PLAYERS_CONTROLLER_URL}/${id}/`);

      // Update state with fresh data
      this.playerDataState.updatePlayer(id, response.data);

      return response.data;
    } catch (error) {
      this.playerDataState.setPlayerLoading(id, false);
      throw error;
    }
  }

  async setPlayerImage(playerPhoto: FormData, playerId: string): Promise<void> {
    await this.apiService.patch<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/setImage/`, playerPhoto);
  }

  async renamePlayer(playerId: string, playerName: string): Promise<void> {
    await this.apiService.put<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/rename/`, { newName: playerName });
  }

  async addPlayer(playerRequestModel: FormData): Promise<PlayerDTO> {
    const response = await this.apiService.post<PlayerDTO>(`${this.PLAYERS_CONTROLLER_URL}/`, playerRequestModel);

    return response.data;
  }

  async deletePlayer(playerId: string): Promise<void> {
    await this.apiService.delete<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}`);
  }

  async getFreeAgents(): Promise<PlayerDTO[]> {
    const response = await this.apiService.get<PlayerDTO[]>(`${this.PLAYERS_CONTROLLER_URL}/freeAgents`);

    return response.data;
  }

  async getPlayerForm(playerId: string, forceRefresh: boolean = false): Promise<PlayerLastGamesForm> {
    console.log('PlayerService.getPlayerForm called with:', { playerId, forceRefresh });

    // Check if we have cached data and it's not stale
    if (!forceRefresh && !this.playerDataState.isFormStale(playerId)) {
      const cachedForm = this.playerDataState.getPlayerForm(playerId);
      if (cachedForm) {
        console.log('Returning cached form:', cachedForm);
        return cachedForm;
      }
    }

    // Set loading state
    this.playerDataState.setFormLoading(playerId, true);
    console.log('Set loading state for player:', playerId);

    try {
      const url = `${this.PLAYERS_CONTROLLER_URL}/${playerId}/form`;
      console.log('Making API call to:', url);

      const response = await this.apiService.get<PlayerLastGamesForm>(url);
      console.log('API response received:', response);

      // Update state with fresh data
      this.playerDataState.updatePlayerForm(playerId, response.data);
      console.log('Updated player form state with data:', response.data);

      return response.data;
    } catch (error: any) {
      console.error('Error in getPlayerForm:', error);
      console.error('Error details:', {
        status: error?.status,
        message: error?.message,
        url: `${this.PLAYERS_CONTROLLER_URL}/${playerId}/form`
      });
      this.playerDataState.setFormLoading(playerId, false);
      throw error;
    }
  }

  async getPlayerStatsByPosition(playerId: string): Promise<{ [position: string]: IPlayerStats }> {
    const response = await this.apiService.get<{ [position: string]: IPlayerStats }>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/statsByPosition`);

    return response.data;
  }

  async editPlayerAge(playerId: string, age: number): Promise<void> {
    await this.apiService.put<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/editPlayerAge/`, { age: age });
  }

  async editPlayerPosition(playerId: string, position: string): Promise<void> {
    await this.apiService.put<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/editPlayerPosition/`, { position: position });
  }

  async editPlayerPlayablePositions(playerId: string, playablePositions: string[]): Promise<void> {
    await this.apiService.put<void>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/editPlayerPlayablePositions/`, { playablePositions: playablePositions });
  }

  async playerSearchByText(searchText: string): Promise<PlayerDTO[]> {
    const response = await this.apiService.get<PlayerDTO[]>(`${this.PLAYERS_CONTROLLER_URL}/playerSearch`, { params: { searchText: searchText } });

    return response.data;
  }

  async getTransferHistoryByPlayerId(playerId: string): Promise<ITransferHistoryEntry[]> {
    const response = await this.apiService.get<ITransferHistoryEntry[]>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/transferHistory`);

    return response.data;
  }

  async comparePlayersById(player1Id: string, player2Id: string): Promise<PlayerComparisonData> {
    const response = await this.apiService.get<PlayerComparisonData>(`${this.PLAYERS_CONTROLLER_URL}/compare/${player1Id}/${player2Id}`);

    return response.data;
  }

  async getPlayerSeasonHistory(playerId: string): Promise<PlayerSeasonHistoryData> {
    const response = await this.apiService.get<PlayerSeasonHistoryData>(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/seasonHistory`);

    return response.data;
  }

  async deletePlayerSeasonHistory(playerId: string, seasonNumber: number): Promise<void> {
    await this.apiService.delete(`${this.PLAYERS_CONTROLLER_URL}/${playerId}/seasonHistory/${seasonNumber}`);
  }
}