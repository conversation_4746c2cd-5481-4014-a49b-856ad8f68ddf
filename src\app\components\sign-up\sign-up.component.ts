import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NotificationService } from '../../services/notification.service';
import { AuthService, RegisterRequest } from '../../services/auth.service';
// Removed old social login - using modern Google Identity Services instead

@Component({
  selector: 'sign-up',
  templateUrl: './sign-up.component.html',
  styleUrl: './sign-up.component.scss'
})
export class SignUpComponent implements OnInit {
  signUpFormGroup: FormGroup = new FormGroup({});
  playerSearchForm: FormGroup = new FormGroup({});
  isLoading = false;
  isSearching = false;
  showPlayerSearch = false;
  availablePlayers: any[] = [];
  selectedPlayer: any = null;

  formControls = [
    { control: new FormControl('', Validators.required), field: 'firstName', displayText: 'First Name', type: 'text-input', maxLength: 20 },
    { control: new FormControl('', Validators.required), field: 'lastName', displayText: 'Last Name', type: 'text-input', maxLength: 20 },
    { control: new FormControl('', [Validators.required, Validators.email]), field: 'email', displayText: 'Email', type: 'email', maxLength: 50 },
    { control: new FormControl('', [Validators.required, Validators.minLength(8)]), field: 'password', displayText: 'Password', type: 'password' },
    { control: new FormControl('', Validators.required), field: 'confirmedPassword', displayText: 'Confirm Password', type: 'password' },
  ];

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit() {
    this.loadFormControl();
    this.initializePlayerSearchForm();

    // Check if user is already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    // Google Sign-In removed - using modern Google Identity Services in login component
  }

  loadFormControl() {
    let group: any = {};
    this.formControls.forEach(item => {
      group[item.field] = item.control;
    });

    this.signUpFormGroup = new FormGroup(group);
  }

  initializePlayerSearchForm() {
    this.playerSearchForm = new FormGroup({
      searchTerm: new FormControl(''),
      playerEmail: new FormControl('', Validators.email)
    });
  }

  async onSubmit() {
    if (this.signUpFormGroup.valid && !this.isLoading) {
      if (!this.doPasswordsMatch()) {
        this.notificationService.error('Passwords do not match');
        return;
      }

      this.isLoading = true;

      try {
        const registerData: RegisterRequest = {
          firstName: this.signUpFormGroup.get('firstName')?.value,
          lastName: this.signUpFormGroup.get('lastName')?.value,
          email: this.signUpFormGroup.get('email')?.value,
          password: this.signUpFormGroup.get('password')?.value
        };

        await this.authService.register(registerData);
        this.notificationService.success('Account created successfully!');

        // If user selected a player, automatically request association
        if (this.selectedPlayer) {
          try {
            await this.authService.requestPlayerAssociation(this.selectedPlayer.id);
            this.notificationService.success(`Player association request submitted for ${this.selectedPlayer.name}! An admin will review your request.`);
          } catch (error: any) {
            // Don't fail registration if player association fails
            this.notificationService.warning(`Account created successfully, but player association request failed: ${error.message}`);
          }
        }

        this.router.navigate(['/dashboard']);
      } catch (error: any) {
        this.notificationService.error(error.message || 'Registration failed');
      } finally {
        this.isLoading = false;
      }
    }
  }

  private doPasswordsMatch(): boolean {
    return this.signUpFormGroup.get('password')?.value === this.signUpFormGroup.get('confirmedPassword')?.value;
  }

  // Google Sign-Up removed - users can sign up with Google through the login page

  isRequiredForm(control: FormControl) {
    if (control.errors) {
      return control.errors['required'];
    }
    return false;
  }

  isEmailInvalid(control: FormControl) {
    if (control.errors) {
      return control.errors['email'];
    }
    return false;
  }

  isPasswordTooShort(control: FormControl) {
    if (control.errors) {
      return control.errors['minlength'];
    }
    return false;
  }

  signUpWithGoogle() {
    // Redirect to login page for Google Sign-In
    this.router.navigate(['/login']);
  }

  navigateToLogin() {
    this.router.navigate(['/login']);
  }

  // Player search functionality
  togglePlayerSearch(): void {
    this.showPlayerSearch = !this.showPlayerSearch;
    if (this.showPlayerSearch) {
      this.availablePlayers = [];
      this.selectedPlayer = null;
      this.playerSearchForm.reset();
    }
  }

  async searchPlayers(): Promise<void> {
    if (this.isSearching) return;

    const searchTerm = this.playerSearchForm.get('searchTerm')?.value;
    if (!searchTerm || searchTerm.trim().length < 2) {
      this.notificationService.error('Please enter at least 2 characters to search');
      return;
    }

    this.isSearching = true;

    try {
      // Use the public endpoint for player search during sign-up
      this.availablePlayers = await this.authService.searchAvailablePlayersPublic(searchTerm);

      if (this.availablePlayers.length === 0) {
        this.notificationService.info('No available players found');
      }
    } catch (error: any) {
      this.notificationService.error('Failed to search players');
    } finally {
      this.isSearching = false;
    }
  }

  selectPlayer(player: any): void {
    this.selectedPlayer = player;
    this.notificationService.success(`Selected ${player.name} for association after registration`);
  }

  clearSelectedPlayer(): void {
    this.selectedPlayer = null;
  }
}
