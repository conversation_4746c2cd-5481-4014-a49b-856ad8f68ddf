.achievement-history-container {
  background: var(--card-background);
  border-radius: 12px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;

    h3 {
      color: var(--primary-color);
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 10px;
        font-size: 1.2rem;
      }
    }

    .achievement-stats {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;

      .stat-item {
        background: var(--secondary-background);
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
        color: var(--text-secondary);
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          color: var(--primary-color);
        }
      }
    }
  }

  .loading-section {
    text-align: center;
    padding: 40px;

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid var(--border-color);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    p {
      color: var(--text-secondary);
      margin: 0;
    }
  }

  .no-achievements {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);

    i {
      font-size: 3rem;
      color: var(--border-color);
      margin-bottom: 15px;
    }

    h4 {
      margin: 0 0 10px 0;
      color: var(--text-color);
    }

    p {
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .achievement-timeline {
    .year-section {
      margin-bottom: 30px;

      .year-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        h4 {
          color: var(--primary-color);
          margin: 0 15px 0 0;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .year-line {
          flex: 1;
          height: 2px;
          background: linear-gradient(to right, var(--primary-color), transparent);
        }
      }

      .achievements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 15px;

        .achievement-card {
          background: var(--secondary-background);
          border-radius: 10px;
          padding: 20px;
          border-left: 4px solid var(--primary-color);
          display: flex;
          align-items: center;
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
          }

          .achievement-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;

            i {
              color: white;
              font-size: 1.3rem;
            }
          }

          .achievement-content {
            flex: 1;

            .achievement-title {
              font-weight: 600;
              color: var(--text-color);
              margin-bottom: 5px;
              font-size: 1rem;
            }

            .achievement-subtitle {
              color: var(--text-secondary);
              font-size: 0.85rem;
              margin-bottom: 5px;
            }

            .achievement-description {
              color: var(--text-secondary);
              font-size: 0.8rem;
              font-style: italic;
            }
          }

          .achievement-rank {
            .rank-badge {
              width: 35px;
              height: 35px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 0.8rem;
              color: white;

              &.rank-1 {
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                color: #333;
              }

              &.rank-2 {
                background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
                color: #333;
              }

              &.rank-3 {
                background: linear-gradient(45deg, #cd7f32, #daa520);
              }
            }
          }
        }
      }
    }
  }

  // Medal Display Styles
  .medals-container {
    .achievement-category {
      margin-bottom: 40px;

      .category-header {
        margin-bottom: 25px;

        h4 {
          color: var(--primary-color);
          font-size: 1.4rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          margin: 0;

          i {
            margin-right: 12px;
            font-size: 1.2rem;
          }
        }
      }

      .medals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;

        .medal-card {
          background: var(--secondary-background);
          border-radius: 15px;
          padding: 25px;
          text-align: center;
          transition: all 0.4s ease;
          position: relative;
          overflow: hidden;
          border: 2px solid transparent;

          &:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
          }

          &.gold-medal {
            border-color: #ffd700;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);

            &:hover {
              box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
            }
          }

          &.silver-medal {
            border-color: #c0c0c0;
            box-shadow: 0 8px 25px rgba(192, 192, 192, 0.3);

            &:hover {
              box-shadow: 0 15px 35px rgba(192, 192, 192, 0.4);
            }
          }

          &.bronze-medal {
            border-color: #cd7f32;
            box-shadow: 0 8px 25px rgba(205, 127, 50, 0.3);

            &:hover {
              box-shadow: 0 15px 35px rgba(205, 127, 50, 0.4);
            }
          }

          &.special-medal {
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);

            &:hover {
              box-shadow: 0 15px 35px rgba(var(--primary-color-rgb), 0.4);
            }
          }

          .medal-container {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;

            .medal-circle {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto;
              position: relative;
              box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
              border: 3px solid rgba(255, 255, 255, 0.2);

              i {
                color: white;
                font-size: 2rem;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
              }

              &::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                border-radius: 50%;
                background: linear-gradient(45deg, rgba(255,255,255,0.3), transparent, rgba(255,255,255,0.3));
                z-index: -1;
              }
            }

            .medal-ribbon {
              position: absolute;
              top: 75px;
              left: 50%;
              transform: translateX(-50%);
              width: 20px;
              height: 40px;
              background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
              clip-path: polygon(0 0, 100% 0, 100% 80%, 50% 100%, 0 80%);
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            }
          }

          .medal-info {
            .medal-title {
              font-size: 1.1rem;
              font-weight: 700;
              color: var(--text-color);
              margin-bottom: 8px;
              line-height: 1.3;
            }

            .medal-season {
              font-size: 0.95rem;
              color: var(--primary-color);
              font-weight: 600;
              margin-bottom: 5px;
            }

            .medal-league {
              font-size: 0.85rem;
              color: var(--text-secondary);
              margin-bottom: 8px;
              font-style: italic;
            }

            .medal-stats {
              font-size: 0.8rem;
              color: var(--text-secondary);
              background: var(--background-color);
              padding: 6px 12px;
              border-radius: 15px;
              display: inline-block;
              margin-top: 5px;
            }
          }

          // Shine effect on hover
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s ease;
          }

          &:hover::before {
            left: 100%;
          }
        }
      }
    }
  }

  .achievement-summary {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 2px solid var(--border-color);

    h4 {
      color: var(--primary-color);
      margin-bottom: 20px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;

      .summary-item {
        background: var(--secondary-background);
        padding: 15px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .summary-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          i {
            color: white;
            font-size: 1.1rem;
          }
        }

        .summary-content {
          .summary-title {
            font-size: 0.9rem;
            color: var(--text-color);
            margin-bottom: 3px;
          }

          .summary-count {
            font-size: 0.8rem;
            color: var(--text-secondary);
            font-weight: 600;
          }
        }
      }
    }
  }

  .recent-achievement {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);

    h4 {
      color: var(--primary-color);
      margin-bottom: 15px;
    }

    .recent-card {
      background: var(--secondary-background);
      border-radius: 10px;
      padding: 20px;
      border: 2px solid var(--primary-color);
      display: flex;
      align-items: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .recent-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        flex-shrink: 0;

        i {
          color: white;
          font-size: 1.5rem;
        }
      }

      .recent-content {
        .recent-title {
          font-weight: 600;
          color: var(--text-color);
          margin-bottom: 5px;
          font-size: 1.1rem;
        }

        .recent-subtitle {
          color: var(--text-secondary);
          font-size: 0.9rem;
          margin-bottom: 5px;
        }

        .recent-description {
          color: var(--text-secondary);
          font-size: 0.85rem;
          font-style: italic;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .achievement-history-container {
    padding: 20px;

    .header {
      flex-direction: column;
      align-items: flex-start;

      .achievement-stats {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .achievement-timeline {
      .year-section {
        .achievements-grid {
          grid-template-columns: 1fr;

          .achievement-card {
            padding: 15px;

            .achievement-icon {
              width: 40px;
              height: 40px;
              margin-right: 12px;

              i {
                font-size: 1.1rem;
              }
            }
          }
        }
      }
    }

    .achievement-summary {
      .summary-grid {
        grid-template-columns: 1fr;
      }
    }

    .recent-achievement {
      .recent-card {
        flex-direction: column;
        text-align: center;

        .recent-icon {
          margin-right: 0;
          margin-bottom: 15px;
        }
      }
    }

    // Medal responsive styles
    .medals-container {
      .achievement-category {
        .medals-grid {
          grid-template-columns: 1fr;
          gap: 20px;

          .medal-card {
            padding: 20px;

            .medal-container {
              margin-bottom: 15px;

              .medal-circle {
                width: 70px;
                height: 70px;

                i {
                  font-size: 1.8rem;
                }
              }

              .medal-ribbon {
                top: 65px;
                width: 18px;
                height: 35px;
              }
            }

            .medal-info {
              .medal-title {
                font-size: 1rem;
              }

              .medal-season {
                font-size: 0.9rem;
              }

              .medal-league {
                font-size: 0.8rem;
              }

              .medal-stats {
                font-size: 0.75rem;
                padding: 5px 10px;
              }
            }
          }
        }
      }
    }
  }
}
